<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>充值次数卡密系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, button {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .row {
            display: flex;
            gap: 20px;
        }
        .col {
            flex: 1;
        }
    </style>
</head>
<body>
    <h1>充值次数卡密系统</h1>
    
    <div class="row">
        <!-- 生成卡密 -->
        <div class="col">
            <div class="container">
                <h2>生成卡密</h2>
                <div class="form-group">
                    <label>卡类型:</label>
                    <input type="text" id="cardType" value="10次卡" placeholder="例如：10次卡">
                </div>
                <div class="form-group">
                    <label>充值次数:</label>
                    <input type="number" id="rechargeTimes" value="10" min="10">
                </div>
                <div class="form-group">
                    <label>生成数量:</label>
                    <input type="number" id="count" value="1" min="1">
                </div>
                <div class="form-group">
                    <label>有效期(天):</label>
                    <input type="number" id="validDays" value="365" min="1">
                </div>
                <button onclick="generateCards()">生成卡密</button>
                <div id="generateResult" class="result" style="display:none;"></div>
            </div>
        </div>

        <!-- 充值 -->
        <div class="col">
            <div class="container">
                <h2>充值</h2>
                <div class="form-group">
                    <label>用户ID:</label>
                    <input type="text" id="userId" placeholder="输入用户ID">
                </div>
                <div class="form-group">
                    <label>卡密:</label>
                    <input type="text" id="cardCode" placeholder="输入卡密">
                </div>
                <button onclick="recharge()">充值</button>
                <div id="rechargeResult" class="result" style="display:none;"></div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 查询用户次数 -->
        <div class="col">
            <div class="container">
                <h2>查询用户次数</h2>
                <div class="form-group">
                    <label>用户ID:</label>
                    <input type="text" id="queryUserId" placeholder="输入用户ID">
                </div>
                <button onclick="queryUserTimes()">查询次数</button>
                <div id="queryResult" class="result" style="display:none;"></div>
            </div>
        </div>

        <!-- 使用次数 -->
        <div class="col">
            <div class="container">
                <h2>使用次数</h2>
                <div class="form-group">
                    <label>用户ID:</label>
                    <input type="text" id="useUserId" placeholder="输入用户ID">
                </div>
                <div class="form-group">
                    <label>使用次数:</label>
                    <input type="number" id="usedTimes" value="1" min="1">
                </div>
                <div class="form-group">
                    <label>使用类型:</label>
                    <input type="text" id="usageType" placeholder="例如：下载文件">
                </div>
                <button onclick="useTimes()">使用次数</button>
                <button onclick="testDownload()" style="background-color: #28a745; margin-top: 5px;">测试下载文件</button>
                <button onclick="simpleDownload()" style="background-color: #17a2b8; margin-top: 5px;">简单下载测试</button>
                <button onclick="debugDownload()" style="background-color: #dc3545; margin-top: 5px;">调试下载</button>
                <button onclick="alertTest()" style="background-color: #ffc107; margin-top: 5px;">测试JavaScript</button>
                <div id="useResult" class="result" style="display:none;"></div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = '/api';

        function showResult(elementId, message, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
            element.style.display = 'block';
        }

        async function generateCards() {
            const data = {
                cardType: document.getElementById('cardType').value,
                rechargeTimes: parseInt(document.getElementById('rechargeTimes').value),
                count: parseInt(document.getElementById('count').value),
                validDays: parseInt(document.getElementById('validDays').value)
            };

            try {
                const response = await fetch(`${API_BASE}/cards/generate`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                const result = await response.json();
                
                if (result.code === 200) {
                    const cards = result.data.map(card => card.cardCode).join('\n');
                    showResult('generateResult', `生成成功！\n卡密列表：\n${cards}`, true);
                } else {
                    showResult('generateResult', `生成失败：${result.message}`, false);
                }
            } catch (error) {
                showResult('generateResult', `请求失败：${error.message}`, false);
            }
        }

        async function recharge() {
            const data = {
                userId: document.getElementById('userId').value,
                cardCode: document.getElementById('cardCode').value
            };

            try {
                const response = await fetch(`${API_BASE}/cards/recharge`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                const result = await response.json();
                
                if (result.code === 200) {
                    showResult('rechargeResult', '充值成功！', true);
                } else {
                    showResult('rechargeResult', `充值失败：${result.message}`, false);
                }
            } catch (error) {
                showResult('rechargeResult', `请求失败：${error.message}`, false);
            }
        }

        async function queryUserTimes() {
            const userId = document.getElementById('queryUserId').value;

            try {
                const response = await fetch(`${API_BASE}/user-times/${userId}`);
                const result = await response.json();
                
                if (result.code === 200) {
                    const data = result.data;
                    const message = `用户次数信息：
可用次数：${data.availableTimes}
总充值次数：${data.totalRecharged}
总使用次数：${data.totalUsed}
更新时间：${data.updatedAt}`;
                    showResult('queryResult', message, true);
                } else {
                    showResult('queryResult', `查询失败：${result.message}`, false);
                }
            } catch (error) {
                showResult('queryResult', `请求失败：${error.message}`, false);
            }
        }

        async function useTimes() {
            const userId = document.getElementById('useUserId').value;
            const usedTimes = parseInt(document.getElementById('usedTimes').value);
            const usageType = document.getElementById('usageType').value;

            // 验证输入
            if (!userId) {
                showResult('useResult', '请输入用户ID', false);
                return;
            }

            showResult('useResult', '正在验证用户并使用次数...', true);

            try {
                const data = {
                    userId: userId,
                    usedTimes: usedTimes,
                    usageType: usageType
                };

                const response = await fetch(`${API_BASE}/user-times/use`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                console.log('后端验证结果:', result);

                if (result.code === 200) {
                    // 只有后端验证成功才下载文件
                    showResult('useResult', '使用次数成功！正在下载文件...', true);
                    downloadFile(userId, usageType);
                } else {
                    // 后端验证失败，不下载文件
                    showResult('useResult', `使用失败：${result.message}`, false);
                }
            } catch (error) {
                console.log('后端验证失败:', error.message);
                showResult('useResult', `请求失败：${error.message}`, false);
            }
        }

        function downloadFile(userId, usageType) {
            console.log('开始下载文件，用户ID:', userId, '使用类型:', usageType);

            try {
                // 创建文件内容
                const currentTime = new Date().toLocaleString('zh-CN');
                const fileContent = `==========================================
        用户文件下载记录
==========================================

用户ID: ${userId}
下载时间: ${currentTime}
使用类型: ${usageType || '文件下载'}
文件版本: v1.0

==========================================
        文件内容说明
==========================================

这是一个示例文件，用于演示充值次数卡密系统的文件下载功能。

系统功能包括：
1. 卡密生成与管理
2. 用户充值功能
3. 次数使用与记录
4. 文件下载服务

感谢使用充值次数卡密系统！

==========================================
        技术支持
==========================================

如有问题，请联系系统管理员。
系统版本: 1.0.0
更新日期: ${currentTime}

==========================================`;

                // 方法1：使用data URL
                const dataStr = "data:text/plain;charset=utf-8," + encodeURIComponent(fileContent);
                const downloadAnchorNode = document.createElement('a');
                downloadAnchorNode.setAttribute("href", dataStr);
                downloadAnchorNode.setAttribute("download", `user_${userId}_${new Date().getTime()}.txt`);

                // 确保链接可见并点击
                downloadAnchorNode.style.position = 'absolute';
                downloadAnchorNode.style.left = '-9999px';
                document.body.appendChild(downloadAnchorNode);

                // 强制点击
                downloadAnchorNode.click();

                // 延迟移除
                setTimeout(() => {
                    document.body.removeChild(downloadAnchorNode);
                }, 1000);

                // 更新结果显示
                showResult('useResult', '文件下载成功！请检查浏览器下载文件夹。', true);
                console.log('文件下载完成');

            } catch (error) {
                console.error('下载文件失败:', error);
                showResult('useResult', `下载失败：${error.message}`, false);

                // 备用方法：显示文件内容让用户手动保存
                fallbackDownload(userId, usageType);
            }
        }

        function fallbackDownload(userId, usageType) {
            const currentTime = new Date().toLocaleString('zh-CN');
            const fileContent = `用户ID: ${userId}
下载时间: ${currentTime}
使用类型: ${usageType || '文件下载'}

这是您的下载文件内容，请手动复制并保存为txt文件。`;

            // 创建一个文本区域显示内容
            const textarea = document.createElement('textarea');
            textarea.value = fileContent;
            textarea.style.position = 'fixed';
            textarea.style.left = '50%';
            textarea.style.top = '50%';
            textarea.style.transform = 'translate(-50%, -50%)';
            textarea.style.width = '400px';
            textarea.style.height = '300px';
            textarea.style.zIndex = '9999';
            textarea.style.backgroundColor = 'white';
            textarea.style.border = '2px solid #333';
            textarea.style.padding = '10px';

            document.body.appendChild(textarea);
            textarea.select();

            // 5秒后自动移除
            setTimeout(() => {
                if (document.body.contains(textarea)) {
                    document.body.removeChild(textarea);
                }
            }, 5000);

            showResult('useResult', '请复制文本区域的内容并手动保存为文件。', true);
        }

        function testDownload() {
            const userId = document.getElementById('useUserId').value || 'test_user';
            const usageType = document.getElementById('usageType').value || '测试下载';

            console.log('测试下载，用户ID:', userId);
            showResult('useResult', '正在测试下载...', true);
            downloadFile(userId, usageType);
        }

        function simpleDownload() {
            console.log('简单下载测试');

            // 最简单的下载方法
            const content = "这是一个测试文件\n下载时间: " + new Date().toLocaleString();
            const element = document.createElement('a');
            element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(content));
            element.setAttribute('download', 'test_' + Date.now() + '.txt');
            element.style.display = 'none';

            document.body.appendChild(element);
            element.click();
            document.body.removeChild(element);

            showResult('useResult', '简单下载测试完成！', true);
            console.log('简单下载完成');
        }

        function debugDownload() {
            console.log('开始调试下载');
            showResult('useResult', '正在调试下载功能...', true);

            try {
                // 检查浏览器支持
                console.log('浏览器信息:', navigator.userAgent);
                console.log('是否支持下载属性:', 'download' in document.createElement('a'));

                // 尝试多种下载方法
                const content = "调试测试文件\n时间: " + new Date().toISOString();

                // 方法1: data URL
                console.log('尝试方法1: data URL');
                const dataUrl = 'data:text/plain;charset=utf-8,' + encodeURIComponent(content);
                console.log('Data URL长度:', dataUrl.length);

                const link1 = document.createElement('a');
                link1.href = dataUrl;
                link1.download = 'debug_test1.txt';
                link1.style.display = 'none';

                document.body.appendChild(link1);
                console.log('点击链接1');
                link1.click();

                setTimeout(() => {
                    document.body.removeChild(link1);
                    console.log('移除链接1');
                }, 1000);

                // 方法2: Blob URL
                setTimeout(() => {
                    console.log('尝试方法2: Blob URL');
                    const blob = new Blob([content], { type: 'text/plain' });
                    const blobUrl = URL.createObjectURL(blob);
                    console.log('Blob URL:', blobUrl);

                    const link2 = document.createElement('a');
                    link2.href = blobUrl;
                    link2.download = 'debug_test2.txt';
                    link2.style.display = 'none';

                    document.body.appendChild(link2);
                    console.log('点击链接2');
                    link2.click();

                    setTimeout(() => {
                        URL.revokeObjectURL(blobUrl);
                        document.body.removeChild(link2);
                        console.log('清理链接2');
                    }, 1000);
                }, 2000);

                // 方法3: 强制新窗口
                setTimeout(() => {
                    console.log('尝试方法3: 新窗口');
                    const newWindow = window.open('', '_blank');
                    if (newWindow) {
                        newWindow.document.write('<pre>' + content + '</pre>');
                        newWindow.document.title = 'debug_test3.txt';
                        console.log('新窗口已打开');
                    } else {
                        console.log('新窗口被阻止');
                    }
                }, 4000);

                showResult('useResult', '调试完成，请查看控制台和下载文件夹', true);

            } catch (error) {
                console.error('调试过程中出错:', error);
                showResult('useResult', '调试出错: ' + error.message, false);
            }
        }

        function alertTest() {
            alert('JavaScript正常工作！');
            console.log('JavaScript测试成功');
            showResult('useResult', 'JavaScript测试完成', true);

            // 尝试最简单的下载
            setTimeout(() => {
                try {
                    const link = document.createElement('a');
                    link.href = 'data:text/plain;charset=utf-8,' + encodeURIComponent('这是一个测试文件\n时间: ' + new Date());
                    link.download = 'simple_test.txt';
                    link.style.display = 'none';
                    document.body.appendChild(link);

                    console.log('创建下载链接:', link);
                    console.log('链接href:', link.href);
                    console.log('链接download:', link.download);

                    link.click();
                    console.log('点击下载链接');

                    setTimeout(() => {
                        document.body.removeChild(link);
                        console.log('移除下载链接');
                        showResult('useResult', '下载测试完成，请检查下载文件夹', true);
                    }, 1000);

                } catch (e) {
                    console.error('下载测试失败:', e);
                    alert('下载测试失败: ' + e.message);
                }
            }, 1000);
        }

        // 备用方法：前端生成文件下载
        function downloadFileLocal(userId, usageType) {
            // 创建一个示例文件内容
            const fileContent = `用户文件下载记录
用户ID: ${userId}
下载时间: ${new Date().toLocaleString()}
使用类型: ${usageType || '文件下载'}
文件说明: 这是一个示例文件，用于演示下载功能。

感谢使用充值次数卡密系统！`;

            // 创建Blob对象
            const blob = new Blob([fileContent], { type: 'text/plain;charset=utf-8' });

            // 创建下载链接
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = `user_${userId}_${new Date().getTime()}.txt`;

            // 触发下载
            document.body.appendChild(a);
            a.click();

            // 清理
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            // 更新结果显示
            setTimeout(() => {
                showResult('useResult', '使用次数成功！文件已下载。', true);
            }, 1000);
        }
    </script>
</body>
</html>
