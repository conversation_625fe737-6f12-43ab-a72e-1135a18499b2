package com.example.rechargecard.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.rechargecard.entity.UsageRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 使用记录Mapper接口
 */
@Mapper
public interface UsageRecordMapper extends BaseMapper<UsageRecord> {

    /**
     * 根据用户ID查询使用记录
     */
    @Select("SELECT * FROM usage_records WHERE user_id = #{userId} AND deleted = 0 ORDER BY created_at DESC")
    List<UsageRecord> selectByUserId(@Param("userId") String userId);

    /**
     * 统计用户总使用次数
     */
    @Select("SELECT COALESCE(SUM(used_times), 0) FROM usage_records WHERE user_id = #{userId} AND deleted = 0")
    Integer sumUsedTimesByUserId(@Param("userId") String userId);

    /**
     * 根据使用类型查询记录
     */
    @Select("SELECT * FROM usage_records WHERE usage_type = #{usageType} AND deleted = 0 ORDER BY created_at DESC")
    List<UsageRecord> selectByUsageType(@Param("usageType") String usageType);

    /**
     * 查询最近的使用记录
     */
    @Select("SELECT * FROM usage_records WHERE deleted = 0 ORDER BY created_at DESC LIMIT #{limit}")
    List<UsageRecord> selectRecentRecords(@Param("limit") Integer limit);
}
