package com.example.rechargecard.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.rechargecard.entity.UserTimes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 用户次数Mapper接口
 */
@Mapper
public interface UserTimesMapper extends BaseMapper<UserTimes> {

    /**
     * 根据用户ID查询用户次数信息
     */
    @Select("SELECT * FROM user_times WHERE user_id = #{userId} AND deleted = 0")
    UserTimes selectByUserId(@Param("userId") String userId);

    /**
     * 增加用户可用次数（充值）
     */
    @Update("UPDATE user_times SET " +
            "available_times = available_times + #{times}, " +
            "total_recharged = total_recharged + #{times}, " +
            "updated_at = NOW() " +
            "WHERE user_id = #{userId} AND deleted = 0")
    int addAvailableTimes(@Param("userId") String userId, @Param("times") Integer times);

    /**
     * 扣减用户可用次数（使用）
     */
    @Update("UPDATE user_times SET " +
            "available_times = available_times - #{times}, " +
            "total_used = total_used + #{times}, " +
            "updated_at = NOW() " +
            "WHERE user_id = #{userId} AND available_times >= #{times} AND deleted = 0")
    int deductAvailableTimes(@Param("userId") String userId, @Param("times") Integer times);

    /**
     * 检查用户是否有足够的可用次数
     */
    @Select("SELECT available_times >= #{times} FROM user_times WHERE user_id = #{userId} AND deleted = 0")
    Boolean hasEnoughTimes(@Param("userId") String userId, @Param("times") Integer times);

    /**
     * 获取用户当前可用次数
     */
    @Select("SELECT available_times FROM user_times WHERE user_id = #{userId} AND deleted = 0")
    Integer getAvailableTimes(@Param("userId") String userId);
}
