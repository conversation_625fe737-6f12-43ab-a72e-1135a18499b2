package com.example.rechargecard.dto;

import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 使用次数请求DTO
 */
@Data
public class UsageRequest {

    /**
     * 用户ID
     */
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    /**
     * 使用次数
     */
    @NotNull(message = "使用次数不能为空")
    @Min(value = 1, message = "使用次数至少为1")
    private Integer usedTimes;

    /**
     * 使用类型/场景
     */
    private String usageType;
}
