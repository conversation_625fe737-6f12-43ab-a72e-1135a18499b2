package com.example.rechargecard.service;

import com.example.rechargecard.dto.CardGenerateRequest;
import com.example.rechargecard.dto.RechargeRequest;
import com.example.rechargecard.entity.RechargeCard;

import java.util.List;

/**
 * 充值卡服务接口
 */
public interface RechargeCardService {

    /**
     * 生成卡密
     */
    List<RechargeCard> generateCards(CardGenerateRequest request);

    /**
     * 充值
     */
    boolean recharge(RechargeRequest request);

    /**
     * 根据卡密查询卡信息
     */
    RechargeCard getCardByCode(String cardCode);

    /**
     * 根据批次号查询卡密列表
     */
    List<RechargeCard> getCardsByBatchId(String batchId);

    /**
     * 验证卡密是否有效
     */
    boolean validateCard(String cardCode);

    /**
     * 更新过期卡密状态
     */
    int updateExpiredCards();

    /**
     * 查询即将过期的卡密
     */
    List<RechargeCard> getExpiringSoonCards();

    /**
     * 统计卡密状态
     */
    Object getCardStatistics();
}
