package com.example.rechargecard.service.impl;

import com.example.rechargecard.dto.CardGenerateRequest;
import com.example.rechargecard.dto.RechargeRequest;
import com.example.rechargecard.entity.RechargeCard;
import com.example.rechargecard.entity.RechargeRecord;
import com.example.rechargecard.mapper.RechargeCardMapper;
import com.example.rechargecard.mapper.RechargeRecordMapper;
import com.example.rechargecard.service.RechargeCardService;
import com.example.rechargecard.service.UserTimesService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 充值卡服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RechargeCardServiceImpl implements RechargeCardService {

    private final RechargeCardMapper rechargeCardMapper;
    private final RechargeRecordMapper rechargeRecordMapper;
    private final UserTimesService userTimesService;

    @Override
    @Transactional
    public List<RechargeCard> generateCards(CardGenerateRequest request) {
        List<RechargeCard> cards = new ArrayList<>();
        String batchId = request.getBatchId();
        if (batchId == null || batchId.trim().isEmpty()) {
            batchId = "BATCH_" + System.currentTimeMillis();
        }

        LocalDateTime expireAt = LocalDateTime.now().plusDays(request.getValidDays());

        for (int i = 0; i < request.getCount(); i++) {
            RechargeCard card = new RechargeCard();
            card.setCardCode(generateCardCode());
            card.setCardType(request.getCardType());
            card.setRechargeTimes(request.getRechargeTimes());
            card.setStatus(RechargeCard.CardStatus.unused);
            card.setExpireAt(expireAt);
            card.setBatchId(batchId);
            card.setCreatedAt(LocalDateTime.now());

            rechargeCardMapper.insert(card);
            cards.add(card);
        }

        log.info("生成卡密成功，批次号：{}，数量：{}", batchId, request.getCount());
        return cards;
    }

    @Override
    @Transactional
    public boolean recharge(RechargeRequest request) {
        // 1. 验证卡密
        RechargeCard card = rechargeCardMapper.selectByCardCode(request.getCardCode());
        if (card == null) {
            throw new RuntimeException("卡密不存在");
        }

        if (card.getStatus() != RechargeCard.CardStatus.unused) {
            throw new RuntimeException("卡密已被使用或已过期");
        }

        if (card.getExpireAt() != null && card.getExpireAt().isBefore(LocalDateTime.now())) {
            throw new RuntimeException("卡密已过期");
        }

        // 2. 检查是否已被使用
        RechargeRecord existRecord = rechargeRecordMapper.selectByCardCode(request.getCardCode());
        if (existRecord != null) {
            throw new RuntimeException("卡密已被使用");
        }

        // 3. 更新卡密状态
        int updateResult = rechargeCardMapper.updateCardAsUsed(
                request.getCardCode(),
                LocalDateTime.now(),
                request.getUserId()
        );

        if (updateResult == 0) {
            throw new RuntimeException("卡密状态更新失败");
        }

        // 4. 增加用户次数
        boolean addResult = userTimesService.addTimes(request.getUserId(), card.getRechargeTimes());
        if (!addResult) {
            throw new RuntimeException("用户次数增加失败");
        }

        // 5. 记录充值记录
        RechargeRecord record = new RechargeRecord();
        record.setUserId(request.getUserId());
        record.setCardCode(request.getCardCode());
        record.setRechargeTimes(card.getRechargeTimes());
        record.setCreatedAt(LocalDateTime.now());
        rechargeRecordMapper.insert(record);

        log.info("充值成功，用户：{}，卡密：{}，次数：{}", 
                request.getUserId(), request.getCardCode(), card.getRechargeTimes());
        return true;
    }

    @Override
    public RechargeCard getCardByCode(String cardCode) {
        return rechargeCardMapper.selectByCardCode(cardCode);
    }

    @Override
    public List<RechargeCard> getCardsByBatchId(String batchId) {
        return rechargeCardMapper.selectByBatchId(batchId);
    }

    @Override
    public boolean validateCard(String cardCode) {
        RechargeCard card = rechargeCardMapper.selectByCardCode(cardCode);
        if (card == null) {
            return false;
        }

        if (card.getStatus() != RechargeCard.CardStatus.unused) {
            return false;
        }

        if (card.getExpireAt() != null && card.getExpireAt().isBefore(LocalDateTime.now())) {
            return false;
        }

        return true;
    }

    @Override
    public int updateExpiredCards() {
        return rechargeCardMapper.updateExpiredCards();
    }

    @Override
    public List<RechargeCard> getExpiringSoonCards() {
        return rechargeCardMapper.selectExpiringSoon();
    }

    @Override
    public Object getCardStatistics() {
        return rechargeCardMapper.countByStatus();
    }

    /**
     * 生成卡密
     */
    private String generateCardCode() {
        // 生成16位卡密：RC + 14位随机字符
        String prefix = "RC";
        String uuid = UUID.randomUUID().toString().replace("-", "").toUpperCase();
        return prefix + uuid.substring(0, 14);
    }
}
