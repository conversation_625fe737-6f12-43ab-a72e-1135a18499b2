package com.example.rechargecard.service.impl;

import com.example.rechargecard.dto.UsageRequest;
import com.example.rechargecard.entity.UsageRecord;
import com.example.rechargecard.entity.UserTimes;
import com.example.rechargecard.mapper.UsageRecordMapper;
import com.example.rechargecard.mapper.UserTimesMapper;
import com.example.rechargecard.service.UserTimesService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 用户次数服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserTimesServiceImpl implements UserTimesService {

    private final UserTimesMapper userTimesMapper;
    private final UsageRecordMapper usageRecordMapper;

    @Override
    public UserTimes getUserTimes(String userId) {
        UserTimes userTimes = userTimesMapper.selectByUserId(userId);
        if (userTimes == null) {
            userTimes = createUserTimes(userId);
        }
        return userTimes;
    }

    @Override
    @Transactional
    public UserTimes createUserTimes(String userId) {
        UserTimes userTimes = new UserTimes();
        userTimes.setUserId(userId);
        userTimes.setAvailableTimes(0);
        userTimes.setTotalRecharged(0);
        userTimes.setTotalUsed(0);
        userTimes.setCreatedAt(LocalDateTime.now());
        userTimes.setUpdatedAt(LocalDateTime.now());

        userTimesMapper.insert(userTimes);
        log.info("创建用户次数记录成功，用户ID：{}", userId);
        return userTimes;
    }

    @Override
    @Transactional
    public boolean addTimes(String userId, Integer times) {
        // 确保用户记录存在
        UserTimes userTimes = getUserTimes(userId);
        
        // 增加可用次数
        int updateResult = userTimesMapper.addAvailableTimes(userId, times);
        if (updateResult > 0) {
            log.info("用户次数增加成功，用户ID：{}，增加次数：{}", userId, times);
            return true;
        }
        
        log.error("用户次数增加失败，用户ID：{}，增加次数：{}", userId, times);
        return false;
    }

    @Override
    @Transactional
    public boolean useTimes(UsageRequest request) {
        String userId = request.getUserId();
        Integer usedTimes = request.getUsedTimes();

        // 检查是否有足够次数
        if (!hasEnoughTimes(userId, usedTimes)) {
            throw new RuntimeException("可用次数不足");
        }

        // 扣减次数
        int updateResult = userTimesMapper.deductAvailableTimes(userId, usedTimes);
        if (updateResult == 0) {
            throw new RuntimeException("次数扣减失败");
        }

        // 获取剩余次数
        Integer remainingTimes = getAvailableTimes(userId);

        // 记录使用记录
        UsageRecord usageRecord = new UsageRecord();
        usageRecord.setUserId(userId);
        usageRecord.setUsedTimes(usedTimes);
        usageRecord.setRemainingTimes(remainingTimes);
        usageRecord.setUsageType(request.getUsageType());
        usageRecord.setCreatedAt(LocalDateTime.now());
        usageRecordMapper.insert(usageRecord);

        log.info("使用次数成功，用户ID：{}，使用次数：{}，剩余次数：{}", 
                userId, usedTimes, remainingTimes);
        return true;
    }

    @Override
    public boolean hasEnoughTimes(String userId, Integer times) {
        Boolean result = userTimesMapper.hasEnoughTimes(userId, times);
        return result != null && result;
    }

    @Override
    public Integer getAvailableTimes(String userId) {
        Integer times = userTimesMapper.getAvailableTimes(userId);
        return times != null ? times : 0;
    }
}
