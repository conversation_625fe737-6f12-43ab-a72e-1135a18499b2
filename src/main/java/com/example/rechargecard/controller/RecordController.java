package com.example.rechargecard.controller;

import com.example.rechargecard.common.Result;
import com.example.rechargecard.entity.RechargeRecord;
import com.example.rechargecard.entity.UsageRecord;
import com.example.rechargecard.mapper.RechargeRecordMapper;
import com.example.rechargecard.mapper.UsageRecordMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 记录查询控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/records")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class RecordController {

    private final RechargeRecordMapper rechargeRecordMapper;
    private final UsageRecordMapper usageRecordMapper;

    /**
     * 获取用户充值记录
     */
    @GetMapping("/recharge/{userId}")
    public Result<List<RechargeRecord>> getRechargeRecords(@PathVariable String userId) {
        try {
            List<RechargeRecord> records = rechargeRecordMapper.selectByUserId(userId);
            return Result.success(records);
        } catch (Exception e) {
            log.error("获取充值记录失败", e);
            return Result.error("获取充值记录失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户使用记录
     */
    @GetMapping("/usage/{userId}")
    public Result<List<UsageRecord>> getUsageRecords(@PathVariable String userId) {
        try {
            List<UsageRecord> records = usageRecordMapper.selectByUserId(userId);
            return Result.success(records);
        } catch (Exception e) {
            log.error("获取使用记录失败", e);
            return Result.error("获取使用记录失败：" + e.getMessage());
        }
    }

    /**
     * 获取最近充值记录
     */
    @GetMapping("/recharge/recent")
    public Result<List<RechargeRecord>> getRecentRechargeRecords(@RequestParam(defaultValue = "10") Integer limit) {
        try {
            List<RechargeRecord> records = rechargeRecordMapper.selectRecentRecords(limit);
            return Result.success(records);
        } catch (Exception e) {
            log.error("获取最近充值记录失败", e);
            return Result.error("获取最近充值记录失败：" + e.getMessage());
        }
    }

    /**
     * 获取最近使用记录
     */
    @GetMapping("/usage/recent")
    public Result<List<UsageRecord>> getRecentUsageRecords(@RequestParam(defaultValue = "10") Integer limit) {
        try {
            List<UsageRecord> records = usageRecordMapper.selectRecentRecords(limit);
            return Result.success(records);
        } catch (Exception e) {
            log.error("获取最近使用记录失败", e);
            return Result.error("获取最近使用记录失败：" + e.getMessage());
        }
    }
}
