package com.example.rechargecard.controller;

import com.example.rechargecard.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 文件下载控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/download")
@CrossOrigin(origins = "*")
public class FileDownloadController {

    /**
     * 下载用户文件
     */
    @GetMapping("/user-file/{userId}")
    public ResponseEntity<Resource> downloadUserFile(@PathVariable String userId,
                                                   @RequestParam(required = false) String usageType) {
        try {
            // 创建文件内容
            String fileContent = generateFileContent(userId, usageType);
            
            // 创建资源
            ByteArrayResource resource = new ByteArrayResource(fileContent.getBytes(StandardCharsets.UTF_8));
            
            // 生成文件名
            String fileName = String.format("user_%s_%s.txt", userId, System.currentTimeMillis());
            
            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"");
            headers.add(HttpHeaders.CONTENT_TYPE, "text/plain; charset=utf-8");
            
            log.info("用户 {} 下载文件: {}", userId, fileName);
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(resource.contentLength())
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);
                    
        } catch (Exception e) {
            log.error("文件下载失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取下载链接
     */
    @PostMapping("/get-link")
    public Result<String> getDownloadLink(@RequestBody DownloadRequest request) {
        try {
            String downloadUrl = String.format("/api/download/user-file/%s?usageType=%s", 
                    request.getUserId(), 
                    request.getUsageType() != null ? request.getUsageType() : "文件下载");
            
            return Result.success("获取下载链接成功", downloadUrl);
        } catch (Exception e) {
            log.error("获取下载链接失败", e);
            return Result.error("获取下载链接失败：" + e.getMessage());
        }
    }

    /**
     * 生成文件内容
     */
    private String generateFileContent(String userId, String usageType) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String currentTime = LocalDateTime.now().format(formatter);
        
        return String.format("""
                ==========================================
                        用户文件下载记录
                ==========================================
                
                用户ID: %s
                下载时间: %s
                使用类型: %s
                文件版本: v1.0
                
                ==========================================
                        文件内容说明
                ==========================================
                
                这是一个示例文件，用于演示充值次数卡密系统的文件下载功能。
                
                系统功能包括：
                1. 卡密生成与管理
                2. 用户充值功能
                3. 次数使用与记录
                4. 文件下载服务
                
                感谢使用充值次数卡密系统！
                
                ==========================================
                        技术支持
                ==========================================
                
                如有问题，请联系系统管理员。
                系统版本: 1.0.0
                更新日期: %s
                
                ==========================================
                """, userId, currentTime, usageType != null ? usageType : "文件下载", currentTime);
    }

    /**
     * 下载请求DTO
     */
    public static class DownloadRequest {
        private String userId;
        private String usageType;

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getUsageType() {
            return usageType;
        }

        public void setUsageType(String usageType) {
            this.usageType = usageType;
        }
    }
}
