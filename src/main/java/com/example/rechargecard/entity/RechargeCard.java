package com.example.rechargecard.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 充值卡实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("recharge_cards")
public class RechargeCard {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 卡密
     */
    @TableField("card_code")
    private String cardCode;

    /**
     * 卡类型
     */
    @TableField("card_type")
    private String cardType;

    /**
     * 可充值次数
     */
    @TableField("recharge_times")
    private Integer rechargeTimes;

    /**
     * 状态：unused-未使用，used-已使用，expired-已过期
     */
    @TableField("status")
    private CardStatus status;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 使用时间
     */
    @TableField("used_at")
    private LocalDateTime usedAt;

    /**
     * 使用者ID
     */
    @TableField("used_by")
    private String usedBy;

    /**
     * 过期时间
     */
    @TableField("expire_at")
    private LocalDateTime expireAt;

    /**
     * 批次号
     */
    @TableField("batch_id")
    private String batchId;

    /**
     * 逻辑删除标记
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 卡状态枚举
     */
    public enum CardStatus {
        unused, used, expired
    }
}
