package com.example.rechargecard.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 充值记录实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("recharge_records")
public class RechargeRecord {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 卡密
     */
    @TableField("card_code")
    private String cardCode;

    /**
     * 充值次数
     */
    @TableField("recharge_times")
    private Integer rechargeTimes;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 逻辑删除标记
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}
