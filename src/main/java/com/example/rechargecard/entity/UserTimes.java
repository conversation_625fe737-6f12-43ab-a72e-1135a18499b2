package com.example.rechargecard.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户次数实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user_times")
public class UserTimes {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 可用次数
     */
    @TableField("available_times")
    private Integer availableTimes;

    /**
     * 总充值次数
     */
    @TableField("total_recharged")
    private Integer totalRecharged;

    /**
     * 总使用次数
     */
    @TableField("total_used")
    private Integer totalUsed;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 逻辑删除标记
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}
