package com.example.rechargecard.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 使用记录实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("usage_records")
public class UsageRecord {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 使用次数
     */
    @TableField("used_times")
    private Integer usedTimes;

    /**
     * 剩余次数
     */
    @TableField("remaining_times")
    private Integer remainingTimes;

    /**
     * 使用类型/场景
     */
    @TableField("usage_type")
    private String usageType;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 逻辑删除标记
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}
