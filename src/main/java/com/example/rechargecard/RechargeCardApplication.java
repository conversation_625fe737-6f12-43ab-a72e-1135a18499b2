package com.example.rechargecard;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 充值次数卡密系统启动类
 */
@SpringBootApplication
@MapperScan("com.example.rechargecard.mapper")
public class RechargeCardApplication {

    public static void main(String[] args) {
        SpringApplication.run(RechargeCardApplication.class, args);
        System.out.println("充值次数卡密系统启动成功！");
        System.out.println("访问地址: http://localhost:8080");
        System.out.println("Druid监控: http://localhost:8080/druid");
    }
}
